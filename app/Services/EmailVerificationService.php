<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

final class EmailVerificationService
{
    private const VERIFICATION_CODE_LENGTH = 6;
    private const CACHE_TTL = 900; // 15 minutes in seconds - verification code expiry time
    private const RETRY_INTERVAL = 180; // 3 minutes in seconds - minimum interval between resend attempts
    private const CACHE_PREFIX = 'email_verification:';
    private const LAST_SENT_PREFIX = 'email_verification_sent:';

    /**
     * Check if a new verification code can be sent (respects retry interval).
     */
    public function canResendCode(string $email): bool
    {
        $lastSentKey = self::LAST_SENT_PREFIX . $email;
        $lastSentTime = Cache::get($lastSentKey);

        if ($lastSentTime === null) {
            return true; // No previous code sent
        }

        $timeSinceLastSent = now()->timestamp - $lastSentTime;
        return $timeSinceLastSent >= self::RETRY_INTERVAL;
    }

    /**
     * Get remaining time before next resend is allowed.
     */
    public function getResendCooldown(string $email): int
    {
        $lastSentKey = self::LAST_SENT_PREFIX . $email;
        $lastSentTime = Cache::get($lastSentKey);

        if ($lastSentTime === null) {
            return 0; // Can send immediately
        }

        $timeSinceLastSent = now()->timestamp - $lastSentTime;
        $remainingCooldown = self::RETRY_INTERVAL - $timeSinceLastSent;

        return max(0, $remainingCooldown);
    }

    /**
     * Generate and send verification code to email address.
     * Will replace existing code if retry interval has passed.
     */
    public function sendVerificationCode(string $email): string
    {
        // Check if we can send a new code
        if (!$this->canResendCode($email)) {
            $cooldown = $this->getResendCooldown($email);
            Log::warning('Verification code resend blocked due to retry interval', [
                'email' => $email,
                'cooldown_seconds' => $cooldown,
            ]);
            throw new \Exception("Please wait {$cooldown} seconds before requesting a new verification code.");
        }

        $code = $this->generateVerificationCode();

        // Store code in cache with email as key (this will replace any existing code)
        $cacheKey = self::CACHE_PREFIX . $email;
        Cache::put($cacheKey, $code, self::CACHE_TTL);

        // Record the time when this code was sent
        $lastSentKey = self::LAST_SENT_PREFIX . $email;
        Cache::put($lastSentKey, now()->timestamp, self::CACHE_TTL);

        // Mock email sending (replace with actual email service later)
        $this->mockSendEmail($email, $code);

        Log::info('Verification code sent', [
            'email' => $email,
            'code' => $code, // Remove this in production
            'expires_at' => now()->addSeconds(self::CACHE_TTL)->toISOString(),
            'next_resend_allowed_at' => now()->addSeconds(self::RETRY_INTERVAL)->toISOString(),
        ]);

        return $code;
    }

    /**
     * Verify the provided code against cached value.
     */
    public function verifyCode(string $email, string $code): bool
    {
        $cacheKey = self::CACHE_PREFIX . $email;
        $cachedCode = Cache::get($cacheKey);

        if ($cachedCode === null) {
            Log::warning('Verification code not found or expired', ['email' => $email]);
            return false;
        }

        $isValid = $cachedCode === $code;

        if ($isValid) {
            // Remove both verification code and last sent time after successful verification
            Cache::forget($cacheKey);
            Cache::forget(self::LAST_SENT_PREFIX . $email);
            Log::info('Verification code verified successfully', ['email' => $email]);
        } else {
            Log::warning('Invalid verification code provided', ['email' => $email]);
        }

        return $isValid;
    }

    /**
     * Check if verification code exists for email.
     */
    public function hasValidCode(string $email): bool
    {
        $cacheKey = self::CACHE_PREFIX . $email;
        return Cache::has($cacheKey);
    }

    /**
     * Get remaining TTL for verification code.
     * Note: Laravel Cache doesn't provide TTL info, so we return a mock value for testing
     */
    public function getCodeTTL(string $email): int
    {
        $cacheKey = self::CACHE_PREFIX . $email;
        if (Cache::has($cacheKey)) {
            return 600; // Mock TTL for testing
        }
        return -2; // Redis-like behavior for non-existent keys
    }

    /**
     * Generate a random 6-digit verification code.
     */
    private function generateVerificationCode(): string
    {
        return str_pad((string) random_int(0, 999999), self::VERIFICATION_CODE_LENGTH, '0', STR_PAD_LEFT);
    }

    /**
     * Mock email sending implementation.
     * Replace this with actual email service integration.
     */
    private function mockSendEmail(string $email, string $code): void
    {
        // Mock implementation - log the email content
        Log::info('Mock email sent', [
            'to' => $email,
            'subject' => 'Email Verification Code',
            'body' => "Your verification code is: {$code}. This code will expire in 15 minutes.",
        ]);

        // In a real implementation, you would integrate with:
        // - Laravel Mail facade
        // - Third-party email services (SendGrid, Mailgun, etc.)
        // - Queue jobs for async email sending
    }
}
