# Email Verification Service

## 概述

`EmailVerificationService` 提供了邮箱验证码的生成、发送和验证功能，现在包含了重试间隔控制机制，防止用户频繁请求验证码。

## 功能特性

### 1. 验证码管理
- 生成6位数字验证码
- 验证码有效期：15分钟 (`CACHE_TTL = 900秒`)
- 验证成功后自动清理缓存

### 2. 重试间隔控制
- 重试间隔：3分钟 (`RETRY_INTERVAL = 180秒`)
- 防止用户因网络问题频繁请求新验证码
- 验证成功后立即清除重试限制

## 常量配置

```php
private const CACHE_TTL = 900;        // 15分钟 - 验证码失效时间
private const RETRY_INTERVAL = 180;   // 3分钟 - 重新获取验证码的最小间隔
```

## 主要方法

### `sendVerificationCode(string $email): string`
发送验证码到指定邮箱地址。

**行为：**
- 检查重试间隔，如果未到时间则抛出异常
- 生成新的6位验证码
- 替换任何现有的验证码
- 记录发送时间用于重试控制
- 返回生成的验证码

**异常：**
- 如果在重试间隔内调用，抛出 `Exception` 并提示等待时间

### `canResendCode(string $email): bool`
检查是否可以重新发送验证码。

**返回：**
- `true`: 可以发送（首次发送或已过重试间隔）
- `false`: 需要等待（在重试间隔内）

### `getResendCooldown(string $email): int`
获取距离下次可发送验证码的剩余秒数。

**返回：**
- `0`: 可以立即发送
- `> 0`: 需要等待的秒数

### `verifyCode(string $email, string $code): bool`
验证提供的验证码。

**行为：**
- 验证成功后清理验证码和重试时间缓存
- 允许用户立即请求新的验证码

## API 端点使用

### 发送验证码
```http
POST /api/v1/users/send-verification-code
Content-Type: application/json

{
    "email": "<EMAIL>"
}
```

**成功响应 (200):**
```json
{
    "success": true,
    "message": "Verification code sent to your email address",
    "data": {
        "message": "Verification code sent successfully",
        "expires_in_seconds": 900,
        "retry_after_seconds": 180
    }
}
```

**重试限制响应 (422):**
```json
{
    "success": false,
    "message": "Please wait before requesting a new verification code.",
    "errors": {
        "retry_after_seconds": 120
    }
}
```

## 使用示例

### 基本用法
```php
$emailService = app(EmailVerificationService::class);

// 检查是否可以发送
if ($emailService->canResendCode($email)) {
    $code = $emailService->sendVerificationCode($email);
    // 发送成功
} else {
    $cooldown = $emailService->getResendCooldown($email);
    // 需要等待 $cooldown 秒
}

// 验证码验证
$isValid = $emailService->verifyCode($email, $userInputCode);
```

### 错误处理
```php
try {
    $code = $emailService->sendVerificationCode($email);
    // 处理成功情况
} catch (\Exception $e) {
    // $e->getMessage() 包含用户友好的错误信息
    // 例如: "Please wait 120 seconds before requesting a new verification code."
}
```

## 缓存键结构

- 验证码存储：`email_verification:{email}`
- 发送时间记录：`email_verification_sent:{email}`

## 测试

运行相关测试：
```bash
make artisan cmd="test tests/Unit/Services/EmailVerificationServiceTest.php"
make artisan cmd="test tests/Feature/Api/V1/UserControllerTest.php"
```

## 注意事项

1. **时间同步**：确保服务器时间准确，重试间隔依赖于时间戳比较
2. **缓存依赖**：功能依赖 Laravel Cache，确保 Redis 或其他缓存驱动正常工作
3. **异常处理**：调用 `sendVerificationCode` 时务必捕获异常
4. **生产环境**：移除日志中的验证码明文记录
